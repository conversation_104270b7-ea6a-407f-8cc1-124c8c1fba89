terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_database_cluster" "example" {
  name = "db-ops-admin"
}

data "digitalocean_database_connection_pool" "read-only" {
  cluster_id = data.digitalocean_database_cluster.example.id
  name       = "ops-admin-pool"
}
